# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-grafana
  description: "UDS Core Grafana"
  url: https://grafana.com/grafana

variables:
  - name: DOMAIN
    description: "Cluster domain"
    default: "uds.dev"

  - name: ADMIN_DOMAIN
    description: "Domain for admin services, defaults to `admin.DOMAIN`"

components:
  - name: grafana
    required: true
    only:
      flavor: upstream
    import:
      path: common
    charts:
      - name: grafana
        valuesFiles:
          - values/upstream-values.yaml
    images:
      - docker.io/grafana/grafana:12.0.1
      - docker.io/curlimages/curl:8.14.1
      - docker.io/library/busybox:1.37.0
      - ghcr.io/kiwigrid/k8s-sidecar:1.30.3

  - name: grafana
    required: true
    only:
      flavor: registry1
    import:
      path: common
    charts:
      - name: grafana
        valuesFiles:
          - values/registry1-values.yaml
    images:
      - registry1.dso.mil/ironbank/opensource/grafana/grafana:12.0.1
      - registry1.dso.mil/ironbank/redhat/ubi/ubi9-minimal:9.6
      - registry1.dso.mil/ironbank/kiwigrid/k8s-sidecar:1.30.3

  - name: grafana
    required: true
    only:
      flavor: unicorn
    import:
      path: common
    charts:
      - name: grafana
        valuesFiles:
          - values/unicorn-values.yaml
    images:
      - quay.io/rfcurated/grafana:12.0.1-jammy-scratch-fips-rfcurated
      - quay.io/rfcurated/busybox:1.37.0-musl-rf.1-fips-rfcurated
      - quay.io/rfcurated/curl:8.14.1-jammy-scratch-fips-rfcurated
      - quay.io/rfcurated/k8s-sidecar:1.30.3-jammy-scratch-fips-rfcurated-rfhardened
