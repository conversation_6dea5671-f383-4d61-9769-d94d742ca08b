# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-loki
  description: "UDS Core Loki"

components:
  - name: loki
    required: true
    description: "Install Loki using upstream (docker) images"
    only:
      flavor: "upstream"
    import:
      path: common
    charts:
      - name: loki
        valuesFiles:
          - ./values/upstream-values.yaml
    images:
      - docker.io/grafana/loki:3.5.1
      - docker.io/nginxinc/nginx-unprivileged:1.28-alpine
      - docker.io/memcached:1.6.38-alpine

  - name: loki
    required: true
    description: "Install Loki using registry1 images"
    only:
      flavor: "registry1"
    import:
      path: common
    charts:
      - name: loki
        valuesFiles:
          - ./values/registry1-values.yaml
    images:
      - registry1.dso.mil/ironbank/opensource/grafana/loki:3.5.1
      - registry1.dso.mil/ironbank/opensource/nginx/nginx-alpine:1.26.3
      - registry1.dso.mil/ironbank/opensource/memcached/memcached:1.6.38

  - name: loki
    required: true
    description: "Install Loki using Rapidfort images"
    only:
      flavor: "unicorn"
    import:
      path: common
    charts:
      - name: loki
        valuesFiles:
          - ./values/unicorn-values.yaml
    images:
      - quay.io/rfcurated/grafana/loki:3.5.1-jammy-fips-rfcurated-rfhardened
      - quay.io/rfcurated/nginx:1.27.5-slim-jammy-fips-rfcurated-rfhardened
      - quay.io/rfcurated/memcached:1.6.38-jammy-fips-rfcurated-rfhardened
