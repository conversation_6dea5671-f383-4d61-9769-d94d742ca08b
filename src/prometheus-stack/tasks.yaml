# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

tasks:
  - name: validate
    actions:
      - description: Validate alert manager
        wait:
          cluster:
            kind: Pod
            name: app.kubernetes.io/name=alertmanager
            namespace: monitoring
            condition: Ready
      - description: Validate prometheus
        wait:
          cluster:
            kind: Pod
            name: app.kubernetes.io/name=prometheus
            namespace: monitoring
            condition: Ready
      - description: Validate kube-state-metrics
        wait:
          cluster:
            kind: Pod
            name: app.kubernetes.io/name=kube-state-metrics
            namespace: monitoring
            condition: Ready
      - description: Validate prometheus node exporter
        wait:
          cluster:
            kind: Pod
            name: app.kubernetes.io/name=prometheus-node-exporter
            namespace: monitoring
            condition: Ready

  - name: gen-crds
    actions:
      - description: Generate servicemonitor types
        cmd: "npx kubernetes-fluent-client crd https://raw.githubusercontent.com/prometheus-operator/prometheus-operator/v0.82.2/example/prometheus-operator-crd/monitoring.coreos.com_servicemonitors.yaml src/pepr/operator/crd/generated/prometheus"
      - description: Generate podmonitor types
        cmd: "npx kubernetes-fluent-client crd https://raw.githubusercontent.com/prometheus-operator/prometheus-operator/v0.82.2/example/prometheus-operator-crd/monitoring.coreos.com_podmonitors.yaml src/pepr/operator/crd/generated/prometheus"
      - description: "Add license headers to generated CRD files"
        shell:
          darwin: bash
          linux: bash
        cmd: |
          # check for addlicense bin
          if [ -x "$HOME/go/bin/addlicense" ]; then
            echo "addlicense installed in $HOME/go/bin"
          else
            echo "Error: addlicense is not installed in $HOME/go/bin" >&2
            exit 1
          fi
          $HOME/go/bin/addlicense -l "AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial" -s=only -v -c "Defense Unicorns" src/pepr/operator/crd/generated
      - description: Pepr Format
        cmd: "npx pepr format"

  - name: e2e-test
    actions:
      - description: "Run Prometheus-Stack E2E tests"
        cmd: |
          npm ci && npx vitest run "prometheus"
        dir: test/vitest
