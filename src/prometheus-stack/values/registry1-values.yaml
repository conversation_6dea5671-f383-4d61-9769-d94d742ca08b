# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

alertmanager:
  alertmanagerSpec:
    image:
      registry: registry1.dso.mil
      repository: ironbank/opensource/prometheus/alertmanager
      tag: v0.28.1
kube-state-metrics:
  image:
    registry: registry1.dso.mil
    repository: ironbank/opensource/kubernetes/kube-state-metrics
    tag: v2.15.0
  securityContext:
    enabled: true
    fsGroup: 65532
    runAsGroup: 65532
    runAsNonRoot: true
    runAsUser: 65532
prometheus:
  prometheusSpec:
    image:
      registry: registry1.dso.mil
      repository: ironbank/opensource/prometheus/prometheus
      tag: v3.3.1
prometheus-node-exporter:
  image:
    registry: registry1.dso.mil
    repository: ironbank/opensource/prometheus/node-exporter
    tag: v1.9.1
prometheusOperator:
  admissionWebhooks:
    containerSecurityContext:
      capabilities:
        drop:
          - ALL
    patch:
      enabled: true
      image:
        registry: registry1.dso.mil
        repository: ironbank/opensource/ingress-nginx/kube-webhook-certgen
        tag: v1.5.3
      securityContext:
        runAsGroup: 65532
        runAsNonRoot: true
        runAsUser: 65532
  image:
    registry: registry1.dso.mil
    repository: ironbank/opensource/prometheus-operator/prometheus-operator
    tag: v0.82.2
  prometheusConfigReloader:
    image:
      registry: registry1.dso.mil
      repository: ironbank/opensource/prometheus-operator/prometheus-config-reloader
      tag: v0.82.2
