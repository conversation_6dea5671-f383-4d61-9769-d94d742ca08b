# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

alertmanager:
  alertmanagerSpec:
    image:
      registry: quay.io
      repository: prometheus/alertmanager
      tag: v0.28.1
kube-state-metrics:
  image:
    registry: registry.k8s.io
    repository: kube-state-metrics/kube-state-metrics
    tag: v2.15.0
  securityContext:
    enabled: true
    fsGroup: 65534
    runAsGroup: 65534
    runAsNonRoot: true
    runAsUser: 65534
prometheus:
  prometheusSpec:
    image:
      registry: quay.io
      repository: prometheus/prometheus
      tag: v3.3.1
prometheus-node-exporter:
  image:
    registry: quay.io
    repository: prometheus/node-exporter
    tag: v1.9.1
prometheusOperator:
  admissionWebhooks:
    patch:
      image:
        registry: registry.k8s.io
        repository: ingress-nginx/kube-webhook-certgen
        tag: v1.5.3
      securityContext:
        runAsGroup: 2000
        runAsNonRoot: true
        runAsUser: 2000
  image:
    registry: quay.io
    repository: prometheus-operator/prometheus-operator
    tag: v0.82.2
  prometheusConfigReloader:
    image:
      registry: quay.io
      repository: prometheus-operator/prometheus-config-reloader
      tag: v0.82.2
