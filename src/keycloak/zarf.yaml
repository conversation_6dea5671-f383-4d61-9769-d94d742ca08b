# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: uds-core-keycloak
  description: "Keycloak"

variables:
  - name: DOMAIN
    description: "Cluster domain"
    default: "uds.dev"

  - name: ADMIN_DOMAIN
    description: "Domain for admin services, defaults to `admin.DOMAIN`"

components:
  - name: keycloak
    required: true
    only:
      flavor: upstream
    import:
      path: common
    charts:
      - name: keycloak
        valuesFiles:
          - "values/upstream-values.yaml"
    images:
      - quay.io/keycloak/keycloak:26.2.5
      - ghcr.io/defenseunicorns/uds/identity-config:0.14.1

  - name: keycloak
    required: true
    only:
      flavor: registry1
    import:
      path: common
    charts:
      - name: keycloak
        valuesFiles:
          - "values/registry1-values.yaml"
    images:
      - registry1.dso.mil/ironbank/opensource/keycloak/keycloak:26.2.5
      - ghcr.io/defenseunicorns/uds/identity-config:0.14.1

  - name: keycloak
    required: true
    only:
      flavor: unicorn
    import:
      path: common
    charts:
      - name: keycloak
        valuesFiles:
          - "values/unicorn-values.yaml"
    images:
      # Keycloak doesn't require the FIPS image as it switches itself into FIPS mode when Bouncy Castle libraries are provided.
      # This happens through the UDS Identity Config. However, in the future we aim to align all the images with their FIPS versions
      # and Keycloak is no exception. Again, this effort doesn't change anything in terms of UDS Security and FIPS compliance posture.
      # The switch is tracked via https://github.com/defenseunicorns/uds-core/issues/1541
      - quay.io/rfcurated/keycloak:26.2.5-jammy-rfcurated
      - ghcr.io/defenseunicorns/uds/identity-config:0.14.1
