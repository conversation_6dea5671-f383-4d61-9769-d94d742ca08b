# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

includes:
  - config: https://raw.githubusercontent.com/defenseunicorns/uds-identity-config/v0.14.1/tasks.yaml

tasks:
  - name: validate
    actions:
      - description: Validate admin interface
        wait:
          network:
            protocol: https
            address: keycloak.admin.uds.dev
            code: 200
      - description: Validate public interface
        wait:
          network:
            protocol: https
            address: sso.uds.dev
            code: 200

  - name: dev-theme
    actions:
      - task: config:dev-theme

  - name: cacert
    description: "Get the CA cert value for the Istio Gateway"
    actions:
      - cmd: |
          # renovate: datasource=docker depName=ghcr.io/defenseunicorns/uds/identity-config versioning=semver
          IMAGE_TAG="0.14.1"
          # This is written to a file because it is larger than the max env size in the shell
          cat <<EOF > tls_cacert.yaml
          tls:
            cacert: "$(docker run --rm --entrypoint sh ghcr.io/defenseunicorns/uds/identity-config:${IMAGE_TAG} -c 'cat /home/<USER>/authorized_certs.pem | base64 -w 0')"
          EOF
          yq eval '.tls.cacert = load("tls_cacert.yaml").tls.cacert' -i src/istio/values/config-admin.yaml
          yq eval '.tls.cacert = load("tls_cacert.yaml").tls.cacert' -i src/istio/values/config-tenant.yaml
          rm tls_cacert.yaml

  - name: debug-istio-traffic
    actions:
      - task: config:debug-istio-traffic

  - name: regenerate-test-pki
    actions:
      - task: config:regenerate-test-pki

  - name: e2e-test
    actions:
      - description: "Run Keycloak E2E tests"
