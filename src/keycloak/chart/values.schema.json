{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "additionalProperties": false, "properties": {"affinity": {"type": "string"}, "additionalGatewayNamespaces": {"type": "array", "items": {"type": "string"}}, "pathParameterProtection": {"type": "boolean"}, "additionalNetworkAllow": {"type": "array", "items": {"type": "object"}}, "autoscaling": {"type": "object", "properties": {"behavior": {"type": "object", "properties": {"scaleDown": {"type": "object", "properties": {"policies": {"type": "array", "items": {"type": "object", "properties": {"periodSeconds": {"type": "number"}, "type": {"type": "string"}, "value": {"type": "number"}}}}, "stabilizationWindowSeconds": {"type": "number"}}}, "scaleUp": {"type": "object", "properties": {"policies": {"type": "array", "items": {"type": "object", "properties": {"periodSeconds": {"type": "number"}, "type": {"type": "string"}, "value": {"type": "number"}}}}, "stabilizationWindowSeconds": {"type": "number"}}}}}, "enabled": {"type": "boolean"}, "labels": {"type": "object", "properties": {}}, "maxReplicas": {"type": "number"}, "metrics": {"type": "array", "items": {"type": "object", "properties": {"resource": {"type": "object", "properties": {"name": {"type": "string"}, "target": {"type": "object", "properties": {"averageUtilization": {"type": "number"}, "type": {"type": "string"}}}}}, "type": {"type": "string"}}}}, "minReplicas": {"type": "number"}}}, "clusterDomain": {"type": "string"}, "configImage": {"type": "string"}, "mtlsClientCert": {"type": "string"}, "debugMode": {"type": "boolean"}, "devMode": {"type": "boolean"}, "domain": {"type": "string"}, "adminDomain": {"type": "string"}, "enableServiceLinks": {"type": "boolean"}, "fips": {"type": "boolean"}, "fipsAllowWeakPasswords": {"type": "boolean"}, "image": {"type": "object", "properties": {"pullPolicy": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "insecureAdminPasswordGeneration": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "username": {"type": "string"}}}, "jsonLogFormat": {"type": "boolean"}, "nodeSelector": {"type": "object", "properties": {}}, "persistence": {"type": "object", "properties": {"accessMode": {"type": "string"}, "conf": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "size": {"type": "string"}}}, "data": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "size": {"type": "string"}}}, "providers": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "size": {"type": "string"}}}, "storageClassName": {"type": "string"}, "themes": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "size": {"type": "string"}}}}}, "podDisruptionBudget": {"type": "object", "properties": {}}, "podLabels": {"type": "object", "properties": {}}, "podManagementPolicy": {"type": "string"}, "podSecurityContext": {"type": "object", "properties": {}}, "postgresql": {"type": "object", "properties": {"database": {"type": "string"}, "host": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "number"}, "username": {"type": "string"}}}, "priorityClassName": {"type": "string"}, "prometheusRule": {"type": "object", "properties": {"annotations": {"type": "object", "properties": {}}, "enabled": {"type": "boolean"}, "labels": {"type": "object", "properties": {}}, "rules": {"type": "array", "items": {}}}}, "realm": {"type": "string"}, "realmInitEnv": {"type": "object", "properties": {"GOOGLE_IDP_ENABLED": {"type": "boolean"}}}, "realmAuthFlows": {"type": "object", "properties": {"USERNAME_PASSWORD_AUTH_ENABLED": {"type": "boolean"}, "X509_AUTH_ENABLED": {"type": "boolean"}, "SOCIAL_AUTH_ENABLED": {"type": "boolean"}, "OTP_ENABLED": {"type": "boolean"}}}, "resources": {"type": "object", "properties": {"limits": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "requests": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}}}, "restartPolicy": {"type": "string"}, "securityContext": {"type": "object", "properties": {}}, "service": {"type": "object", "properties": {"labels": {"type": "object", "properties": {}}, "sessionAffinity": {"type": "string"}, "sessionAffinityConfig": {"type": "object", "properties": {}}}}, "serviceMonitor": {"type": "object", "properties": {"annotations": {"type": "object", "properties": {}}, "enabled": {"type": "boolean"}, "interval": {"type": "string"}, "labels": {"type": "object", "properties": {}}, "namespace": {"type": "string"}, "namespaceSelector": {"type": "object", "properties": {}}, "path": {"type": "string"}, "port": {"type": "string"}, "scheme": {"type": "string"}, "scrapeTimeout": {"type": "string"}, "tlsConfig": {"type": "object", "properties": {}}}}, "smtp": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "port": {"type": "number"}}}, "statefulsetLabels": {"type": "object", "properties": {}}, "terminationGracePeriodSeconds": {"type": "number"}, "tolerations": {"type": "array", "items": {}}, "topologySpreadConstraints": {"type": "null"}, "updateStrategy": {"type": "string"}, "env": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"]}}, "themeCustomizations": {"type": "object", "properties": {"resources": {"type": "object", "properties": {"images": {"type": "array", "items": {"type": "object", "properties": {"name": {"enum": ["background.png", "footer.png", "logo.png", "favicon.png"]}, "configmap": {"type": "object", "properties": {"name": {"type": "string"}}}}}}}}}}, "x509LookupProvider": {"type": "string"}, "migrations": {"type": "object", "properties": {"deleteGeneratedTrustStore": {"type": "boolean"}}}}}