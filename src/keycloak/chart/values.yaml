# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

image:
  # The Keycloak image repository
  repository: quay.io/keycloak/keycloak
  # Overrides the Keycloak image tag whose default is the chart appVersion
  tag: "26.2.5"
  # The Keycloak image pull policy
  pullPolicy: IfNotPresent

# renovate: datasource=github-tags depName=defenseunicorns/uds-identity-config versioning=semver
configImage: ghcr.io/defenseunicorns/uds/identity-config:0.14.1

# The public domain name of the Keycloak server
domain: "###ZARF_VAR_DOMAIN###"
# The admin domain for hosts to trust clients on
adminDomain: '{{ "###ZARF_VAR_ADMIN_DOMAIN###" | default "admin.###ZARF_VAR_DOMAIN###" }}'

# Additional Istio Gateways that expose Keycloak, to allow for client cert usage
# A prefix of `istio-` is required for namespaces to prevent accidental misconfiguration
additionalGatewayNamespaces: []
# Example
# - "istio-login-gateway"

# Enables an EnvoyFilter to block path-parameter bypasses of AuthorizationPolicy rules
# Should only be disabled if this is incorrectly blocking paths you expect to be allowed
pathParameterProtection: true

# The primary Keycloak realm
realm: uds

# UDS Identity Config Realm Environment Variables. More info here: https://github.com/defenseunicorns/uds-identity-config/blob/main/docs/CUSTOMIZE.md#templated-realm-values
realmInitEnv:
  GOOGLE_IDP_ENABLED: false
  # GOOGLE_IDP_ID: ""
  # GOOGLE_IDP_SIGNING_CERT: ""
  # GOOGLE_IDP_NAME_ID_FORMAT: ""
  # GOOGLE_IDP_CORE_ENTITY_ID: ""
  # GOOGLE_IDP_ADMIN_GROUP: ""
  # GOOGLE_IDP_AUDITOR_GROUP: ""
  # PASSWORD_POLICY: "hashAlgorithm(pbkdf2-sha256) and forceExpiredPasswordChange(90) and specialChars(2) and lowerCase(0) and upperCase(0) and passwordHistory(5) and length(12) and notUsername(undefined) and digits(0)"
  # EMAIL_VERIFICATION_ENABLED: true
  # TERMS_AND_CONDITIONS_ENABLED: true
  # X509_OCSP_FAIL_OPEN: true

# UDS Identity Config Authentication Flows Environment Variables. More info here: https://github.com/defenseunicorns/uds-identity-config/blob/main/docs/CUSTOMIZE.md#templated-realm-values
realmAuthFlows:
  USERNAME_PASSWORD_AUTH_ENABLED: true
  X509_AUTH_ENABLED: true
  SOCIAL_AUTH_ENABLED: true
  OTP_ENABLED: true
  WEBAUTHN_ENABLED: false
  X509_MFA_ENABLED: false

# Theme customizations with allowed types. More info here: https://github.com/defenseunicorns/uds-identity-config/blob/main/docs/reference/UDS%20Core/IdAM/customization.md#branding-customizations
themeCustomizations:
  settings:
    # Extra Registration Form Fields ( Affiliation, Pay Grade, Unit Organization or Company Name )
    enableRegistrationFields: true
#   resources:
#     images:
#       - name: background.png
#         configmap:
#           name: keycloak-theme-overrides
#       - name: footer.png
#         configmap:
#           name: keycloak-theme-overrides
#       - name: logo.png
#         configmap:
#           name: keycloak-theme-overrides
#       - name: favicon.png
#         configmap:
#           name: keycloak-theme-overrides

# Generates an initial password for first admin user - only use if install is headless
# (i.e. cannot hit keycloak UI with `zarf connect keycloak`), password should be changed after initial login
insecureAdminPasswordGeneration:
  enabled: false
  username: admin

# Support for custom `network.allow` entries on the Package CR, useful for extra datasources
additionalNetworkAllow: []
# ref: https://uds.defenseunicorns.com/reference/configuration/custom-resources/packages-v1alpha1-cr/#allow
#   - direction: Egress
#     selector:
#       app.kubernetes.io/name: keycloak
#     remoteCidr: **************
#     description: "IDP"
#     port: 443

# Indicates whether information about services should be injected into Pod's environment variables, matching the syntax of Docker links
enableServiceLinks: true

# Pod management policy. One of `Parallel` or `OrderedReady`
podManagementPolicy: Parallel

# StatefulSet's update strategy
updateStrategy: RollingUpdate

# Pod restart policy. One of `Always`, `OnFailure`, or `Never`
restartPolicy: Always

# Termination grace period in seconds for Keycloak shutdown. Clusters with a large cache might need to extend this to give Infinispan more time to rebalance
terminationGracePeriodSeconds: 5

# The internal Kubernetes cluster domain
clusterDomain: cluster.local

# Sets development mode for Keycloak. This disables caching, Postgres and HPAs and should only be used for testing
# Must have no values populated for `postgresql` in order to use
devMode: true

# Enable debug logging for keycloak and quarkus
debugMode: false

# Enable JSON logging format for Keycloak
jsonLogFormat: true

# Enable SMTP networkPolicy and config
smtp:
  enabled: false
  port: 587

# Configure FIPS mode for Keycloak
fips: true
# By default, Keycloak requires 14+ character passwords in FIPS mode. This switch disables this requirement.
fipsAllowWeakPasswords: false

#  Pod priority class name
priorityClassName: ""

# Pod level securityContext
podSecurityContext: {}

# Keycloak container securityContext
securityContext: {}

# Pod affinity
affinity: |
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              {{- include "keycloak.selectorLabels" . | nindent 12 }}
            matchExpressions:
              - key: app.kubernetes.io/component
                operator: NotIn
                values:
                  - test
          topologyKey: failure-domain.beta.kubernetes.io/zone

# Topology spread constraints template
topologySpreadConstraints:

# Node labels for Pod assignment
nodeSelector: {}

# Node taints to tolerate
tolerations: []

# Additional Pod labels
podLabels: {}

# Pod resource requests and limits
resources:
  requests:
    cpu: "500m"
    memory: "512Mi"
  limits:
    cpu: "1"
    memory: "1Gi"

# Persistence settings for PVC management (when enabled=false, an emptyDir will be used)
# `devMode` overrides the enabled flag for `data` and `themes` to allow hot-reloads of the theme/plugin in k3d
#
#  NOTE:
#    Default persistence settings should be sufficient for *nearly all use cases*, especially when used in conjunction with
#      the [uds-identity-config](https://github.com/defenseunicorns/uds-identity-config) image which is included by default.
#      Unique situations may require changes, but care should be taken to account for an appropriate accessMode for your environment.
persistence:
  accessMode: ReadWriteOnce
  storageClassName: ""
  providers:
    enabled: false
    size: "512Mi"
  conf:
    enabled: false
    size: "512Mi"
  data:
    enabled: false
    size: "512Mi"
  themes:
    enabled: false
    size: "512Mi"

# Pod disruption budget
podDisruptionBudget: {}

# Additional labels for the StatefulSet
statefulsetLabels: {}

service:
  # Additional labels for headless and HTTP Services
  labels: {}
  # Session affinity
  # See https://kubernetes.io/docs/concepts/services-networking/service/#proxy-mode-userspace
  sessionAffinity: ""
  # Session affinity config
  sessionAffinityConfig: {}

# Connection information for external postgres database
postgresql:
  # The username of the database user
  username: ""
  # The password of the database user
  password: ""
  # Database name
  database: ""
  # URL for the database
  host: ""
  # Port the database is listening on
  port: 5432
  egressCidr: ""
  # Configure internal postgresql deployment, requires keycloak not be deployed in dev-mode
  internal:
    enabled: false
    remoteSelector: {}
    remoteNamespace: ""

serviceMonitor:
  # If `true`, a ServiceMonitor resource for the prometheus-operator is created
  enabled: false
  # Optionally sets a target namespace in which to deploy the ServiceMonitor resource
  namespace: ""
  # Optionally sets a namespace for the ServiceMonitor
  namespaceSelector: {}
  # Annotations for the ServiceMonitor
  annotations: {}
  # Additional labels for the ServiceMonitor
  labels: {}
  # Interval at which Prometheus scrapes metrics
  interval: 10s
  # Timeout for scraping
  scrapeTimeout: 10s
  # The path at which metrics are served
  path: /metrics
  # The Service port at which metrics are served
  port: metrics
  # added by Big Bang to support Istio mTLS
  scheme: ""
  tlsConfig: {}

prometheusRule:
  # If `true`, a PrometheusRule resource for the prometheus-operator is created
  enabled: false
  # Annotations for the PrometheusRule
  annotations: {}
  # Additional labels for the PrometheusRule
  labels: {}
  # List of rules for Prometheus
  rules: []
  # - alert: keycloak-IngressHigh5xxRate
  #   annotations:
  #     message: The percentage of 5xx errors for keycloak over the last 5 minutes is over 1%.
  #   expr: |
  #     (
  #       sum(
  #         rate(
  #           nginx_ingress_controller_response_duration_seconds_count{exported_namespace="mynamespace",ingress="mynamespace-keycloak",status=~"5[0-9]{2}"}[1m]
  #         )
  #       )
  #       /
  #       sum(
  #         rate(
  #           nginx_ingress_controller_response_duration_seconds_count{exported_namespace="mynamespace",ingress="mynamespace-keycloak"}[1m]
  #         )
  #       )
  #     ) * 100 > 1
  #   for: 5m
  #   labels:
  #     severity: warning

autoscaling:
  # If `true`, an autoscaling/v2beta2 HorizontalPodAutoscaler resource is created (requires Kubernetes 1.18 or above)
  # Autoscaling seems to be most reliable when using KUBE_PING service discovery (see README for details)
  # This disables the `replicas` field in the StatefulSet
  enabled: false
  # Additional HorizontalPodAutoscaler labels
  labels: {}
  # The minimum and maximum number of replicas for the Keycloak StatefulSet
  minReplicas: 3
  maxReplicas: 10
  # The metrics to use for scaling
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80
  # The scaling policy to use. This will scale up quickly but only scale down a single Pod per 5 minutes.
  # This is important because caches are usually only replicated to 2 Pods and if one of those Pods is terminated this will give the cluster time to recover.
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Pods
          value: 1
          periodSeconds: 300

env: []

x509LookupProvider: nginx
mtlsClientCert: "%DOWNSTREAM_PEER_CERT%"

# Custom migrations setting for UDS Core internal usage only
migrations:
  # Deletes the generated truststore to enable recreating it by Keycloak
  deleteGeneratedTrustStore: false
