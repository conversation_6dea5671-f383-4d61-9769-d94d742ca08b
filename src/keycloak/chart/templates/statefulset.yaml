# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "keycloak.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := .Values.statefulsetLabels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "keycloak.selectorLabels" . | nindent 6 }}
  serviceName: {{ include "keycloak.fullname" . }}-headless
  podManagementPolicy: {{ .Values.podManagementPolicy }}
  updateStrategy:
    type: {{ .Values.updateStrategy }}
  template:
    metadata:
      labels:
        {{- include "keycloak.selectorLabels" . | nindent 8 }}
        {{- range $key, $value := .Values.podLabels }}
        {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 8 }}
        {{- end }}
       {{- if not .Values.devMode }}
      annotations:
        postgres-hash: {{ include (print $.Template.BasePath "/secret-postgresql.yaml") . | sha256sum }}
        kc-realm-hash: {{ include (print $.Template.BasePath "/secret-kc-realm.yaml") . | sha256sum }}
      {{- end }}
    spec:
      securityContext:
      {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: uds-config
          image: "{{ .Values.configImage }}"
          securityContext:
            runAsUser: 65532
            runAsGroup: 65532
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
          volumeMounts:
            - name: providers
              mountPath: /opt/keycloak/providers
            - name: data
              mountPath: /opt/keycloak/data
            - name: themes
              mountPath: /opt/keycloak/themes
            - name: conf
              mountPath: /opt/keycloak/conf
            {{- if and .Values.themeCustomizations .Values.themeCustomizations.resources .Values.themeCustomizations.resources.images }}
            - name: theme-overrides
              mountPath: /opt/keycloak/theme-overrides
            {{- end }}
          envFrom:
            - secretRef:
                name: {{ include "keycloak.fullname" . }}-realm-env
        {{- if .Values.migrations.deleteGeneratedTrustStore }}
        - name: remove-generated-truststore
          image: "{{ .Values.configImage }}"
          command: ["sh", "-c", "rm -rf /opt/keycloak/data/keycloak-truststore.p12 ; ls -la /opt/keycloak/data"]
          securityContext:
          {{- toYaml .Values.securityContext | nindent 12 }}
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
          volumeMounts:
            - name: data
              mountPath: /opt/keycloak/data
        {{- end }}
      containers:
        - name: keycloak
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          securityContext:
          {{- toYaml .Values.securityContext | nindent 12 }}
          command:
            - "/opt/keycloak/bin/kc.sh"
          args:
            {{- if .Values.devMode }}
            - "start-dev"
            # Do not cache themes in dev mode to support hot-reloading
            - "--spi-theme-static-max-age=-1"
            - "--spi-theme-cache-themes=false"
            - "--spi-theme-cache-templates=false"
            {{- else }}
            - "start"
            # # Needed for nginx provider
            # - "--auto-build"
            {{- end }}
            # This will only import the realm if it does not exist
            - "--import-realm"
            # FIPS Mode
            {{- if .Values.fips }}
            # Full configuration might be found at https://www.keycloak.org/server/fips
            - "--features=preview,fips"
            - "--fips-mode=strict"
            {{- if .Values.fipsAllowWeakPasswords}}
            # These switches enable the shorter passwords, which do not break FIPS compliance.
            # See https://github.com/keycloak/keycloak/blob/2aaf9ac00868370ee23f49cfecb5d508f40dbee8/testsuite/integration-arquillian/tests/base/src/main/java/org/keycloak/testsuite/arquillian/containers/AbstractQuarkusDeployableContainer.java#L439-L441
            - "--spi-password-hashing-pbkdf2-max-padding-length=14"
            - "--spi-password-hashing-pbkdf2-sha256-max-padding-length=14"
            - "--spi-password-hashing-pbkdf2-sha512-max-padding-length=14"
            {{- end }}
            {{- else }}
            - "--features=preview"
            {{- end }}
            - "--proxy-headers=xforwarded"
            - "--http-enabled=true"
            - "--hostname-strict=false"
            {{- if .Values.jsonLogFormat }}
            - "--log-console-output=json"
            {{- end }}
          envFrom:
            - secretRef:
                name: {{ include "keycloak.fullname" . }}-realm-env
          env:
          {{- if .Values.env }}
            {{- tpl (toYaml .Values.env) . | nindent 12 }}
          {{- end }}
            # Common configuration
            - name: UDS_DOMAIN
              value: "{{ .Values.domain }}"
            - name: UDS_ADMIN_DOMAIN
              value: "{{ tpl .Values.adminDomain . }}"

            # Enable health and metrics endpoints
            - name: KC_HEALTH_ENABLED
              value: "true"
            - name: KC_METRICS_ENABLED
              value: "true"
            - name: KC_EVENT_METRICS_USER_ENABLED
              value: "true"

            # Enable access log
            - name: QUARKUS_HTTP_ACCESS_LOG_ENABLED
              value: "true"

            # X509 configuration
            - name: KC_HTTPS_CLIENT_AUTH
              value: request

            # Escape Slashes in Group Names
            - name: KC_SPI_GROUP_JPA_ESCAPE_SLASHES_IN_GROUP_PATH
              value: "true"

            ## Activate the nginx provider
            - name: KC_SPI_X509CERT_LOOKUP_PROVIDER
              value: {{ .Values.x509LookupProvider }}
            # Set nginx provider header name
            - name: KC_SPI_X509CERT_LOOKUP_NGINX_SSL_CLIENT_CERT
              value: istio-mtls-client-certificate
            # Dumb value (not used in the nginx provider, but required by the SPI)
            - name: KC_SPI_X509CERT_LOOKUP_NGINX_SSL_CLIENT_CERT_CHAIN_PREFIX
              value: UNUSED
          {{- if .Values.fips }}
            # This is a workaround for https://github.com/keycloak/keycloak/issues/39454
            # It silences the TLS Errors coming from DNS_PING out. Once the above issue is resolved and released,
            # this can be removed.
            - name: QUARKUS_LOG_CATEGORY__ORG_BOUNCYCASTLE_JSSE_PROVIDER__LEVEL
              value: WARNING
          {{- end }}
          {{- if or .Values.devMode .Values.debugMode }}
            # Enable debug logs
            - name: KC_LOG_LEVEL
              value: DEBUG
            - name: QUARKUS_LOG_CATEGORY__ORG_APACHE_HTTP__LEVEL
              value: DEBUG
            - name: QUARKUS_LOG_CATEGORY__ORG_KEYCLOAK_SERVICES_X509__LEVEL
              value: TRACE
            # Crypto information, primarily for FIPS debugging
            - name: QUARKUS_LOG_CATEGORY__ORG_KEYCLOAK_COMMON_CRYPTO__LEVEL
              value: TRACE
            - name: QUARKUS_LOG_CATEGORY__ORG_KEYCLOAK_CRYPTO__LEVEL
              value: TRACE
          {{- end }}
          {{- if .Values.devMode }}
            # https://github.com/keycloak/keycloak/issues/39046
            # Starting from 26.2.0, Keycloak doesn't use password for the internal H2 database.
            # This breaks upgrade scenarios, so we need to use the same password as in 26.1.x
            - name: KC_DB_USERNAME
              value: sa
            - name: KC_DB_PASSWORD
              value: password
          {{- end }}
          {{- if eq (include "keycloak.postgresql.config" .) "true" }}
            # Infinispan cache configuration
            - name: KC_CACHE
              value: ispn
            - name: KC_CACHE_STACK
              value: kubernetes
            - name: KC_SPI_STICKY_SESSION_ENCODER_INFINISPAN_SHOULD_ATTACH_ROUTE
              value: "false"
            # java opts for jgroups required for infinispan distributed cache when using the kubernetes stack.
            # https://www.keycloak.org/server/caching
            - name: JAVA_OPTS_APPEND
              value: -Djgroups.dns.query={{ include "keycloak.fullname" . }}-headless.keycloak.svc.cluster.local
            # Postgres database configuration
            - name: KC_DB
              value: postgres
            - name: KC_DB_URL_HOST
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-postgresql
                  key: host
            - name: KC_DB_URL_PORT
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-postgresql
                  key: port
            - name: KC_DB_URL_DATABASE
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-postgresql
                  key: database
            - name: KC_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-postgresql
                  key: username
            - name: KC_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-postgresql
                  key: password
          {{- end }}
          {{- if .Values.insecureAdminPasswordGeneration.enabled }}
            - name: KEYCLOAK_ADMIN
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-admin-password
                  key: username
            - name: KEYCLOAK_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-admin-password
                  key: password
          {{- end }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
            - name: tcp
              containerPort: 7800
              protocol: TCP
            - name: tcp-fd
              containerPort: 57800
              protocol: TCP
            - name: metrics
              containerPort: 9000
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health/live
              port: metrics
              scheme: HTTP
            failureThreshold: 15
            timeoutSeconds: 2
            periodSeconds: 15
            initialDelaySeconds: 10
          readinessProbe:
            httpGet:
              path: /health/ready
              port: metrics
              scheme: HTTP
            failureThreshold: 15
            timeoutSeconds: 2
            initialDelaySeconds: 10
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: providers
              mountPath: /opt/keycloak/providers
            - name: data
              mountPath: /opt/keycloak/data
            - name: themes
              mountPath: /opt/keycloak/themes
            - name: conf
              mountPath: /opt/keycloak/conf
              readOnly: true
            - name: client-secrets
              mountPath: /var/run/secrets/uds/client-secrets
      enableServiceLinks: {{ .Values.enableServiceLinks }}
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- tpl . $ | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl . $ | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      volumes:
        - name: client-secrets
          secret:
            secretName: {{ include "keycloak.fullname" . }}-client-secrets
        - name: providers
          {{- if .Values.persistence.providers.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "keycloak.fullname" . }}-providers
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: conf
          {{- if .Values.persistence.conf.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "keycloak.fullname" . }}-conf
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: data
          {{- if or .Values.persistence.data.enabled .Values.devMode }}
          # devMode enables this PVC by default to preserve legacy behavior
          persistentVolumeClaim:
            claimName: {{ include "keycloak.fullname" . }}-data
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: themes
          {{- if or .Values.persistence.themes.enabled .Values.devMode }}
          # devMode enables this PVC by default to preserve legacy behavior
          persistentVolumeClaim:
            claimName: {{ include "keycloak.fullname" . }}-themes
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- if and .Values.themeCustomizations .Values.themeCustomizations.resources .Values.themeCustomizations.resources.images }}
        - name: theme-overrides
          projected:
            sources:
              {{- range $image := .Values.themeCustomizations.resources.images }}
              {{- if $image.configmap.name }}
              - configMap:
                  name: {{ $image.configmap.name }}
                  items:
                    - key: {{ $image.name }}
                      path: {{ $image.name }}
              {{- end }}
              {{- end }}
        {{- end }}
