{"extends": ["github>defenseunicorns/uds-common//config/renovate.json5", ":semanticCommits", ":semanticCommitTypeAll(chore)"], "ignorePresets": [":ignoreModulesAndTests"], "branchConcurrentLimit": 0, "prConcurrentLimit": 0, "prHourlyLimit": 0, "separateMajorMinor": false, "helm-values": {"ignorePaths": ["src/neuvector/values"]}, "packageRules": [{"matchFileNames": ["src/authservice/**"], "groupName": "authservice", "commitMessageTopic": "authservice"}, {"matchFileNames": ["src/istio/**"], "groupName": "istio", "commitMessageTopic": "istio"}, {"matchFileNames": ["src/vector/**"], "groupName": "vector", "commitMessageTopic": "vector"}, {"matchFileNames": ["src/velero/**"], "groupName": "velero", "commitMessageTopic": "velero"}, {"matchFileNames": ["src/prometheus-stack/**"], "groupName": "prometheus-stack", "commitMessageTopic": "prometheus-stack"}, {"matchFileNames": ["src/neuvector/**"], "groupName": "neuvector", "commitMessageTopic": "neuvector"}, {"matchFileNames": ["src/metrics-server/**"], "groupName": "metrics-server", "commitMessageTopic": "metrics-server"}, {"matchFileNames": ["src/loki/**"], "groupName": "loki", "commitMessageTopic": "loki"}, {"matchFileNames": ["src/keycloak/**"], "groupName": "keycloak", "commitMessageTopic": "keycloak"}, {"matchFileNames": ["src/grafana/**"], "groupName": "grafana", "commitMessageTopic": "grafana"}, {"matchFileNames": ["test/**/*", ".github/**", "bundles/**", "tasks/*.yaml", ".vscode/settings.json", "src/test/**", "README.md", "scripts/**"], "groupName": "support-deps", "commitMessageTopic": "support dependencies"}, {"matchFileNames": ["package.json", "package-lock.json", "tasks/create.yaml"], "groupName": "pepr", "commitMessageTopic": "pepr"}, {"matchPackageNames": ["defenseunicorns/uds-common", "mcr.microsoft.com/playwright"], "groupName": "support-deps", "commitMessageTopic": "support-deps"}]}