# UDS Core Diagrams

## Diagram Explanations
At this time there are three layers to our diagram that visually explain the following:

1. `Overview`, basic view of what applications are present and what other services/applications they are communicating with.

2. `Traffic Direction`, similar to `Overview` layer with the addition of directional arrows to represent the flow of traffic in cluster.

3. `Ports and Protocols`, similar to `Traffic Direction` layer with the addition of ports and protocols.

## Making Changes to Diagrams for UDS Core

Utilize the drawio file to make changes across all diagrams. Make sure that changes are exported in the svg format and put into this directory if being used in the docs as well as any changes to the drawio file.

## How to Customize For Outside Use

To download the [svg from github](https://github.com/defenseunicorns/uds-core/tree/main/docs/.images/diagrams), select one of the svg files, then select the download button in the top right of the `Preview` view.

Alternatively you can also download the svg from the [uds.defenseunicorns.com](https://uds.defenseunicorns.com/reference/uds-core/overview/) docsite by right clicking and selecting the `Save image as` option.

#### If you have suggestions for the diagrams, we welcome issues or pull requests contributions to [uds-core](https://github.com/defenseunicorns/uds-core).
