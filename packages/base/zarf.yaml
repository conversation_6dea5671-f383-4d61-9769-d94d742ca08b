# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: core-base
  description: "UDS Core (Base)"
  authors: "Defense Unicorns - Product"
  # x-release-please-start-version
  version: "0.44.0"
  # x-release-please-end
  x-uds-dependencies: []
  annotations:
    dev.uds.title: UDS Core (Base)
    dev.uds.tagline: Provides the base for all other UDS Core Functional Layers
    dev.uds.category: UDS-Core
    dev.uds.keywords: uds,pepr,istio,operator,policies

components:
  - name: uds-operator-config
    required: true
    import:
      path: ../../src/pepr

  # CRDs
  - name: prometheus-operator-crds
    required: true
    import:
      path: ../../src/prometheus-stack

  # Pepr the world
  - name: pepr-uds-core
    required: true
    import:
      path: ../../src/pepr

  # Istio
  - name: istio-controlplane
    required: true
    import:
      path: ../../src/istio

  - name: gateway-api-crds
    required: true
    import:
      path: ../../src/istio/common

  - name: istio-admin-gateway
    required: true
    import:
      path: ../../src/istio

  - name: istio-tenant-gateway
    required: true
    import:
      path: ../../src/istio

  - name: istio-passthrough-gateway
    required: false
    import:
      path: ../../src/istio

  - name: istio-egress-gateway
    required: false
    import:
      path: ../../src/istio
