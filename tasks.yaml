# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

variables:
  - name: FLAVOR
    default: upstream

  - name: LAYER

includes:
  - create: ./tasks/create.yaml
  - setup: ./tasks/setup.yaml
  - deploy: ./tasks/deploy.yaml
  - test: ./tasks/test.yaml
  - lint: ./tasks/lint.yaml

tasks:
  - name: default
    actions:
      - description: "Build, deploy and test UDS Core"
        task: test-uds-core

  - name: dev-setup
    description: "Create k3d cluster with istio"
    inputs:
      istio_components:
        description: "Comma separated list of istio components to deploy"
        default: ""
    actions:
      - description: "Create the dev cluster"
        task: setup:create-k3d-cluster

      - description: "Register CRDs in cluster"
        cmd: npx ts-node -e "import { registerCRDs } from './src/pepr/operator/crd/register'; registerCRDs()"
        env:
          - "PEPR_MODE=dev"

      # Note: the `registry-url` flag used here requires uds 0.19.2+
      - description: "Deploy the Istio source package with Zarf Dev"
        cmd: "uds zarf dev deploy src/istio --flavor upstream --registry-url docker.io --no-progress --components=${{ .inputs.istio_components }}"

      # Note: Since this is a dev deploy without any `--flavor` it only deploys the CRDs (other components are flavored)
      - description: "Deploy the Prometheus-Stack source package with Zarf Dev to only install the CRDs"
        cmd: "uds zarf dev deploy src/prometheus-stack --no-progress"

      - description: "Dev instructions"
        cmd: |
          echo "Next steps:"
          echo "  - To test & develop the Pepr module, run 'npx pepr dev' from a Javascript debug terminal"
          echo "  - Otherwise run 'npx pepr deploy' to deploy the Pepr module to the cluster"
          echo "  - Additional source packages can be deployed with 'zarf dev deploy src/<package> --flavor upstream'"

  - name: slim-dev
    actions:
      - description: "Build slim dev bundle"
        task: create:k3d-slim-dev-bundle

      - description: "Deploy slim dev bundle"
        task: deploy:k3d-slim-dev-bundle

  - name: dev-identity
    description: "Create k3d cluster with istio, Pepr, Keycloak, and Authservice for development"
    actions:
      - task: dev-setup

      - description: "Deploy Pepr"
        cmd: "npx pepr deploy --confirm"

      - description: "Deploy Keycloak + Authservice"
        cmd: "uds run dev-deploy --set LAYER=identity-authorization --no-progress"

  - name: dev-deploy
    description: "Deploy the given core layer with Zarf Dev"
    actions:
      - cmd: "uds zarf dev deploy packages/${LAYER} --flavor ${FLAVOR} --no-progress"

  - name: setup-cluster
    description: "Create a k3d Cluster and Initialize with Zarf"
    actions:
      - task: setup:k3d-test-cluster

  - name: create-standard-package
    description: "Create UDS Core Zarf Package, `upstream` flavor default, use --set FLAVOR={flavor} to change"
    actions:
      - task: create:standard-package

  - name: test-single-layer
    description: "Deploys k3d cluster, layer dependencies and the provided layer (based on LAYER variable)"
    actions:
      - task: test:layer-dependencies
      - task: test:single-layer

  - name: deploy-standard-bundle
    actions:
      - task: deploy:k3d-standard-bundle

  - name: test-uds-core
    description: "Build and test UDS Core"
    actions:
      - task: test:uds-core

  - name: test-uds-core-multi-node
    description: "Deploys UDS Core on a multi-node cluster (based on K3D_EXTRA_ARGS variable)"
    actions:
      - task: test:uds-core
        with:
          K3D_EXTRA_ARGS: "--servers 3 --agents 2"

  - name: test-uds-core-ha
    description: "Build and test UDS Core"
    actions:
      - task: setup:ha-postgres
      - task: test:uds-core-ha

  - name: test-uds-core-ha-upgrade
    description: "Test an upgrade from the latest released UDS Core package with HA to current branch with HA"
    actions:
      - task: setup:ha-postgres
      - task: test:uds-core-ha-upgrade

  - name: test-uds-core-upgrade
    description: "Test an upgrade from the latest released UDS Core package to current branch"
    actions:
      - task: test:uds-core-upgrade

  - name: test-compliance-compose
    description: "Compose OSCAL Component Definition"
    actions:
      - task: test:local-compliance-compose

  - name: test-compliance-validate
    description: "Validate Compliance of UDS Core to produce Assessment Results"
    actions:
      - task: test:compliance-validate

  - name: test-compliance-evaluate
    description: "Evaluate Compliance of UDS Core against an established threshold"
    actions:
      - task: test:compliance-evaluate

  - name: lint-check
    description: "Run linting checks"
    actions:
      - task: lint:check

  - name: lint-fix
    description: "Fix linting issues"
    actions:
      - task: lint:fix

  - name: lint-oscal
    actions:
      - task: lint:oscal

  # Note that due to cloning the docs repo (which is private) this task will require organization access to the repo
  # This task does not clone in/manage docs outside of the core repo so you may hit some 404s during development
  # This task does not run the integration-script in the uds-docs repo, the sidebar will not be the same as the live docs
  - name: dev-docs
    description: "Start the dev docs server"
    actions:
      - description: "Cleanup previous runs"
        cmd: |
          rm -rf uds-docs
      - description: "Clone the docs repo and symlink the reference docs"
        cmd: |
          git clone https://github.com/defenseunicorns/uds-docs.git uds-docs
          rm -rf uds-docs/src/content/docs/reference uds-docs/src/content/docs/.images
          # This only symlinks the reference and images folders since these are the only docs we use in the docs site
          ln -s $(pwd)/docs/reference uds-docs/src/content/docs/reference
          ln -s $(pwd)/docs/.images uds-docs/src/content/docs/.images
      - description: "Start the docs server with npm (this will run until you stop it)"
        cmd: |
          # Actual startup takes up to a minute because of the npm install
          cd uds-docs && npm i && echo "Sidebar will be different from the live docs, this is expected" && npm run dev && echo ""
