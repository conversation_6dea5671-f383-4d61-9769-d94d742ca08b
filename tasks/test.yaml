# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

variables:
  - name: EXCLUDED_PACKAGES
    description: A comma separated string of packages to be excluded from validation and e2e tests.
    default: ""

includes:
  - create: ./create.yaml
  - setup: ./setup.yaml
  - deploy: ./deploy.yaml
  - util: ./utils.yaml
  - test-resources: ../src/test/tasks.yaml
  - base-layer: ../packages/base/tasks.yaml
  - idam-layer: ../packages/identity-authorization/tasks.yaml
  - common-setup: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.1/tasks/setup.yaml
  - compliance: https://raw.githubusercontent.com/defenseunicorns/uds-common/v1.16.1/tasks/compliance.yaml

tasks:
  - name: base
    description: "Build and test the base layer"
    actions:
      - task: create:pepr-build
      - task: setup:k3d-test-cluster
      - cmd: uds run -f tasks/test.yaml single-layer --set FLAVOR=${FLAVOR} --set=layer=base

  - name: single-layer
    description: "Build and test a single layer, must set UDS_LAYER environment variable"
    inputs:
      create_options:
        default: "--skip-sbom"
        description: "Additional options passed in when creating Zarf packages. Defaults to: --skip-sbom"
    actions:
      - task: create:single-layer
        with:
          layer: ${LAYER}
          create_options: ${{ .inputs.create_options }}
      - task: deploy:single-layer
        with:
          layer: ${LAYER}
      - task: validate-package
        with:
          layer: ${LAYER}

  - name: layer-dependencies
    description: "Sets up a k3d cluster and deploys dependencies for the given layer"
    actions:
      - task: setup:k3d-test-cluster
      - cmd: uds zarf tools yq '.metadata.x-uds-dependencies.[]' packages/${LAYER}/zarf.yaml 2>/dev/null
        mute: true
        setVariables:
          - name: LAYER_DEPS
      - cmd: |
          for dep in $LAYER_DEPS; do
            uds run -f tasks/test.yaml single-layer --set LAYER=$dep --set FLAVOR=${FLAVOR} --no-progress
          done

  - name: validate-packages
    description: "Validate all packages"
    inputs:
      # Added to support bypassing passthrough gateway validation on non-k3d distributions.
      validate_passthrough:
        description: Whether to validate the passthrough gateway.
        default: "true"
    # loop through each src/* package and run the validate.yaml task
    actions:
      - cmd: |
          for package in $(ls src); do
            if [ ! $(echo ${EXCLUDED_PACKAGES} | grep ${package}) ]; then
              if [ ${package} = "istio" ]; then
                uds run -f src/${package}/tasks.yaml validate --no-progress --with validate_passthrough=${{ .inputs.validate_passthrough }}
              else
                uds run -f src/${package}/tasks.yaml validate --no-progress
              fi
            fi
          done
          set +e

  - name: validate-package
    description: "Validate a single package"
    inputs:
      layer:
        description: The UDS Core layer to validate
        required: true
    actions:
      - cmd: |
          uds run -f packages/${{ index .inputs "layer" }}/tasks.yaml validate --no-progress

  - name: e2e-tests
    description: "E2E Test all packages"
    inputs:
      architecture:
        description: "System architecture that the test-apps package should be built for."
        required: true
        default: ${UDS_ARCH}
    # Run each e2e test type from the e2e folder
    actions:
      - description: "Setup the Doug User for testing"
        task: common-setup:keycloak-user
        with:
          group: "/UDS Core/Admin"
      - description: "Create and Deploy Test App Package"
        task: test-resources:create-deploy
        with:
          architecture: ${{ .inputs.architecture }}
      - description: "Run Playwright E2E tests for all packages"
        dir: test/playwright
        cmd: |
          # renovate: datasource=docker depName=mcr.microsoft.com/playwright versioning=docker
          docker run --rm --ipc=host --net=host -e FULL_CORE="true" --mount type=bind,source="$(pwd)",target=/app mcr.microsoft.com/playwright:v1.53.1-noble sh -c " \
            cd app && \
            npm ci && \
            npx playwright test \
            "
      - description: "Run E2E Tests"
        cmd: |
          npm ci
          npx vitest run
        dir: test/vitest
      - description: remove test resources
        task: test-resources:remove

  - name: uds-core
    description: "Build and test UDS Core"
    inputs:
      K3D_EXTRA_ARGS:
        default: ""
    actions:
      - task: create:standard-package
        with:
          create_options: "--skip-sbom"
      - task: create:k3d-standard-bundle
      - task: deploy:k3d-standard-bundle
        with:
          K3D_EXTRA_ARGS: "${{ .inputs.K3D_EXTRA_ARGS }}"
      - task: validate-packages

  - name: uds-core-e2e
    description: "Build and test UDS Core e2e"
    actions:
      - task: uds-core
      - task: e2e-tests

  - name: uds-core-non-k3d
    description: "Validate and Test UDS Core deployment on a non K3d Cluster"
    actions:
      - task: util:setup-hosts
      - task: validate-packages
        with:
          validate_passthrough: "false"
      - task: e2e-tests
        with:
          architecture: "amd64"

  - name: uds-core-ha
    description: "Build and test UDS Core"
    actions:
      - task: create:standard-package
        with:
          create_options: "--skip-sbom"
      - task: create:k3d-standard-bundle
      - task: deploy:k3d-standard-bundle-ha
      - task: validate-packages

  - name: uds-core-ha-upgrade
    description: "Test an upgrade from the latest released UDS Core package with HA to current branch with HA"
    actions:
      - task: deploy:latest-bundle-release-ha
      - task: create:standard-package
        with:
          create_options: "--skip-sbom"
      - task: create:k3d-standard-bundle
      - cmd: uds deploy bundles/k3d-standard/uds-bundle-k3d-core-demo-${UDS_ARCH}-${VERSION}.tar.zst --packages core --confirm --no-progress
        env:
          - UDS_CONFIG=bundles/k3d-standard/uds-ha-config.yaml
      - task: validate-packages
      - task: e2e-tests

  - name: uds-core-upgrade
    description: "Test an upgrade from the latest released UDS Core package to current branch"
    actions:
      - task: setup:k3d-test-cluster
      - task: deploy:latest-package-release
      - task: create:standard-package
        with:
          create_options: "--skip-sbom"
      - task: deploy:standard-package
      - task: validate-packages
      - task: e2e-tests

  - name: compliance-validate
    description: "validate against the required compliance"
    actions:
      - task: compliance:validate
        with:
          oscalfile: ./compliance/oscal-component-composed.yaml
          assessment_results: ./compliance/oscal-assessment-results.yaml
          options: -t il4

  - name: compliance-evaluate
    description: "evaluate against the required compliance"
    actions:
      - task: compliance:evaluate
        with:
          assessment_results: ./compliance/oscal-assessment-results.yaml
          options: -t il4

  - name: slim-dev
    description: "Run validate for the components contained in the slim dev bundle"
    actions:
      - task: base-layer:validate
        with:
          validate_passthrough: "false"
      - task: idam-layer:validate
      - task: test-resources:e2e-test
        with:
          validate_egress: "false"

  - name: local-compliance-compose
    description: "compose oscal component definitions"
    inputs:
      oscalfile:
        description: oscal file to compose
        default: ./compliance/oscal-component.yaml
      options:
        description: for setting additional flags
    actions:
      - cmd: lula tools compose -f ${{ .inputs.oscalfile }} ${{ .inputs.options }}
