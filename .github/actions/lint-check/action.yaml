# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

name: lint-check
description: "Check Project for Linting Errors"

runs:
  using: composite
  steps:
    - name: Use Node.js latest
      uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
      with:
        node-version: 24
    - name: Install UDS CLI
      uses: defenseunicorns/setup-uds@ab842abcad1f7a3305c2538e3dd1950d0daacfa5 # v1.0.1
      with:
        # renovate: datasource=github-tags depName=defenseunicorns/uds-cli versioning=semver
        version: v0.27.7
    - name: Run Formatting Checks
      run: uds run lint-check --no-progress
      shell: bash
